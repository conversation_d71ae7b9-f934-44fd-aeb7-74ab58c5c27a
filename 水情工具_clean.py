import requests
import re
import js2py
import urllib3

class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        # 禁用SSL证书验证
        self.session.verify = False
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """获取水情数据"""
        
        # 初始化会话
        try:
            print("🔍 正在连接主站点...")
            response1 = self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            print(f"🔍 主站点连接成功，状态码: {response1.status_code}")
            
            print("🔍 正在连接NUXT站点...")
            response2 = self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
            print(f"🔍 NUXT站点连接成功，状态码: {response2.status_code}")
        except Exception as e:
            print(f"🔍 网络连接详细错误: {str(e)}")
            return {"错误": f"网络连接失败: {str(e)}"}

        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': '浙江省',
            'ssx': '',  # 空值，获取全省数据
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }

        # 发送请求
        try:
            print("🔍 发送数据请求...")
            响应 = self.session.post(
                "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater",
                data=参数,
                timeout=30
            )
            
            if 响应.status_code == 200:
                print(f"🔍 请求成功，数据长度: {len(响应.text):,} 字符")
                
                # 解析数据
                水情数据 = self.解析数据(响应.text)
                
                return {
                    "数据长度": len(响应.text),
                    "水情数据": 水情数据
                }
            else:
                return {"错误": f"请求失败，状态码: {响应.status_code}"}
                
        except Exception as e:
            return {"错误": f"请求异常: {str(e)}"}

    def 解析数据(self, html内容):
        """使用JavaScript引擎解析NUXT数据"""
        try:
            print("🔍 开始JavaScript解析...")
            
            # 提取NUXT脚本
            pattern = r'window\.__NUXT__=(.*?);'
            match = re.search(pattern, html内容, re.DOTALL)
            
            if not match:
                return [{"说明": "未找到NUXT脚本"}]
            
            nuxt_script = match.group(1)
            print(f"🔍 NUXT脚本长度: {len(nuxt_script)}")
            
            # 执行JavaScript
            js_code = f"var window = {{}}; window.__NUXT__ = {nuxt_script}; window.__NUXT__;"
            result = js2py.eval_js(js_code)
            
            print("🔍 JavaScript执行成功")
            
            # 转换为Python对象
            data = result.to_dict() if hasattr(result, 'to_dict') else dict(result)
            
            # 提取水情数据
            水情列表 = []
            
            if 'data' in data:
                for item in data['data']:
                    if 'waterData' in item:
                        water_data = item['waterData']
                        # 遍历所有站点类型
                        for key in ['cx', 'cjj', 'qt', 'cbz']:
                            if key in water_data:
                                for 站点 in water_data[key]:
                                    站点信息 = {
                                        "序号": 站点.get('index', ''),
                                        "站点ID": 站点.get('zh', ''),
                                        "站点名称": 站点.get('name', ''),
                                        "当前水位": f"{站点.get('sw', 'N/A')}m",
                                        "经度": 站点.get('lon', ''),
                                        "纬度": 站点.get('lat', ''),
                                        "区县": 站点.get('county', ''),
                                        "城市": 站点.get('city', ''),
                                        "更新时间": 站点.get('time', ''),
                                        "站点类型": key
                                    }
                                    水情列表.append(站点信息)
            
            print(f"🔍 解析出 {len(水情列表)} 个站点")
            return 水情列表
            
        except Exception as e:
            print(f"🔍 解析失败: {str(e)}")
            return [{"说明": f"解析失败: {str(e)}"}]


def main():
    """主函数"""
    print("🌊 水情数据获取工具")
    print("=" * 40)

    # 创建水情工具实例
    工具 = 水情工具()

    # 获取水情数据
    结果 = 工具.获取数据("浙江省", "", "RR,ZZ,ZQ,DD,TT,", "4,5,3,2,1,9,")

    if "错误" in 结果:
        print(f"❌ 错误: {结果['错误']}")
        return

    # 显示结果
    水情数据 = 结果.get("水情数据", [])
    print(f"🌊 解析出的水情数据:")
    
    for i, 站点 in enumerate(水情数据, 1):
        print(f"  {i}. {站点}")

if __name__ == "__main__":
    main()
