import requests
import re
import urllib3

class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        # 禁用SSL证书验证
        self.session.verify = False
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """获取水情数据"""
        
        # 初始化会话
        try:
            print("🔍 正在连接主站点...")
            response1 = self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            print(f"🔍 主站点连接成功，状态码: {response1.status_code}")
            
            print("🔍 正在连接NUXT站点...")
            response2 = self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
            print(f"🔍 NUXT站点连接成功，状态码: {response2.status_code}")
        except Exception as e:
            print(f"🔍 网络连接详细错误: {str(e)}")
            return {"错误": f"网络连接失败: {str(e)}"}

        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': '浙江省',
            'ssx': '',  # 空值，获取全省数据
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }

        # 发送请求
        try:
            print("🔍 发送数据请求...")
            响应 = self.session.post(
                "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater",
                data=参数,
                timeout=30
            )
            
            if 响应.status_code == 200:
                print(f"🔍 请求成功，数据长度: {len(响应.text):,} 字符")
                
                # 解析数据
                水情数据 = self.解析数据(响应.text)
                
                return {
                    "数据长度": len(响应.text),
                    "水情数据": 水情数据
                }
            else:
                return {"错误": f"请求失败，状态码: {响应.status_code}"}
                
        except Exception as e:
            return {"错误": f"请求异常: {str(e)}"}

    def 解析数据(self, html内容):
        """使用正则表达式解析NUXT函数参数"""
        try:
            print("🔍 开始正则表达式解析...")
            
            # 提取NUXT函数调用的参数
            pattern = r'window\.__NUXT__=\(function\([^)]*\)\{.*?\}\(([^)]+)\)\);'
            match = re.search(pattern, html内容, re.DOTALL)
            
            if not match:
                return [{"说明": "未找到NUXT函数参数"}]
            
            params_str = match.group(1)
            print(f"🔍 参数字符串长度: {len(params_str)}")
            
            # 解析参数
            return self.解析函数参数(params_str)
            
        except Exception as e:
            print(f"🔍 解析失败: {str(e)}")
            return [{"说明": f"解析失败: {str(e)}"}]

    def 解析函数参数(self, params_str):
        """解析函数参数，提取所有站点信息"""
        
        # 使用正则表达式分割参数
        param_pattern = r'(null|"[^"]*"|-?[0-9]+\.?[0-9]*)'
        params = re.findall(param_pattern, params_str)
        
        print(f"🔍 解析出 {len(params)} 个参数")
        
        # 清理参数
        cleaned_params = []
        for param in params:
            if param == 'null':
                cleaned_params.append(None)
            elif param.startswith('"') and param.endswith('"'):
                cleaned_params.append(param[1:-1])  # 去掉引号
            else:
                try:
                    if '.' in param:
                        cleaned_params.append(float(param))
                    else:
                        cleaned_params.append(int(param))
                except:
                    cleaned_params.append(param)
        
        # 提取站点信息
        站点列表 = []
        i = 0
        
        while i < len(cleaned_params):
            # 查找站点ID（6-8位数字字符串或包含字母的编码）
            if (isinstance(cleaned_params[i], str) and 
                ((cleaned_params[i].isdigit() and 6 <= len(cleaned_params[i]) <= 8) or
                 (len(cleaned_params[i]) >= 6 and 
                  any(c.isdigit() for c in cleaned_params[i]) and 
                  any(c.isalpha() for c in cleaned_params[i])))):
                
                站点ID = cleaned_params[i]
                
                # 在后续参数中查找站点名称、水位、经纬度
                站点名称 = None
                水位 = "N/A"
                经度 = None
                纬度 = None
                
                # 查找范围：当前位置后15个参数内
                for j in range(i + 1, min(i + 16, len(cleaned_params))):
                    param = cleaned_params[j]
                    
                    # 查找站点名称（非空字符串，不是代码）
                    if (站点名称 is None and isinstance(param, str) and 
                        len(param) >= 2 and 
                        not param.isdigit() and
                        param not in ['PZ', 'RR', 'ZZ', 'ZQ', 'DD', 'TT', '-', '1', '2', '0']):
                        站点名称 = param
                    
                    # 查找经纬度（浙江省范围内的坐标）
                    elif isinstance(param, (int, float)):
                        if 119 <= param <= 122 and 经度 is None:
                            # 检查下一个参数是否是纬度
                            if (j + 1 < len(cleaned_params) and 
                                isinstance(cleaned_params[j + 1], (int, float)) and
                                27 <= cleaned_params[j + 1] <= 31):
                                经度 = param
                                纬度 = cleaned_params[j + 1]
                    
                    # 查找水位（数字字符串，合理范围）
                    elif (水位 == "N/A" and isinstance(param, str)):
                        try:
                            val = float(param)
                            if 0 <= val <= 2000:  # 合理的水位范围
                                水位 = param
                        except:
                            pass
                
                # 如果找到了基本信息就记录
                if 站点名称 and 经度 is not None and 纬度 is not None:
                    站点信息 = {
                        "序号": len(站点列表) + 1,
                        "站点ID": 站点ID,
                        "站点名称": 站点名称,
                        "当前水位": f"{水位}m",
                        "经度": 经度,
                        "纬度": 纬度
                    }
                    站点列表.append(站点信息)
                    print(f"🔍 找到站点: {站点名称} (ID: {站点ID})")
            
            i += 1
        
        print(f"🔍 总共解析出 {len(站点列表)} 个站点")
        return 站点列表


def main():
    """主函数"""
    print("🌊 水情数据获取工具 - 正则表达式版本")
    print("=" * 50)

    # 创建水情工具实例
    工具 = 水情工具()

    # 获取水情数据
    结果 = 工具.获取数据("浙江省", "", "RR,ZZ,ZQ,DD,TT,", "4,5,3,2,1,9,")

    if "错误" in 结果:
        print(f"❌ 错误: {结果['错误']}")
        return

    # 显示结果
    水情数据 = 结果.get("水情数据", [])
    print(f"\n🌊 解析出 {len(水情数据)} 个站点:")
    
    for 站点 in 水情数据:
        print(f"  {站点['序号']}. {站点['站点名称']} (ID: {站点['站点ID']})")
        print(f"     水位: {站点['当前水位']}")
        print(f"     坐标: {站点['经度']}, {站点['纬度']}")
        print()

if __name__ == "__main__":
    main()
