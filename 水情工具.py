#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from datetime import datetime


class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        # 禁用SSL证书验证
        self.session.verify = False
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """
        获取水情数据

        """

        # 初始化会话
        try:
            print("🔍 正在连接主站点...")
            response1 = self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            print(f"🔍 主站点连接成功，状态码: {response1.status_code}")

            print("🔍 正在连接NUXT站点...")
            response2 = self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
            print(f"🔍 NUXT站点连接成功，状态码: {response2.status_code}")
        except Exception as e:
            print(f"🔍 网络连接详细错误: {str(e)}")
            return {"错误": f"网络连接失败: {str(e)}"}

        # 设置参数 - 使用新的参数组合
        参数 = {
            'areaFlag': 1,
            'sss': '浙江省',
            'ssx': '',  # 空值，获取全省数据
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
        
        
        # 发送请求
        try:
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)

            # 解析数据
            水情数据 = self.解析数据(响应.text)

            return {
                "状态": "成功",
                "数据长度": len(响应.text),
                "水情数据": 水情数据,
                "查询参数": 参数,
                "URL": 响应.url
            }
        except Exception as e:
            return {"错误": str(e)}

    def 解析数据(self, html内容):
        """从HTML中解析水情数据 - 纯JavaScript引擎解析"""
        import re
        import js2py

        try:
            print("🔍 开始JavaScript解析...")

            # 1. 提取NUXT脚本
            pattern = r'window\.__NUXT__=(.*?);'
            match = re.search(pattern, html内容, re.DOTALL)

            if not match:
                return [{"说明": "未找到NUXT脚本"}]

            nuxt_script = match.group(1)
            print(f"🔍 提取到NUXT脚本，长度: {len(nuxt_script)}")

            # 2. 执行JavaScript代码
            js_code = f"""
            var window = {{}};
            window.__NUXT__ = {nuxt_script};
            window.__NUXT__;
            """

            print("🔍 执行JavaScript...")
            result = js2py.eval_js(js_code)

            if result is None:
                return [{"说明": "JavaScript执行结果为空"}]

            print(f"🔍 JavaScript执行成功")

            # 3. 提取水情数据
            return self.提取水情数据(result)

        except Exception as e:
            print(f"🔍 JavaScript解析失败: {str(e)}")
            return [{"说明": f"JavaScript解析失败: {str(e)}"}]

    def 提取水情数据(self, js_result):
        """从JavaScript结果中提取水情数据"""
        try:
            # 转换为Python对象
            if hasattr(js_result, 'to_dict'):
                data = js_result.to_dict()
            else:
                data = dict(js_result)

            print(f"🔍 数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            水情列表 = []

            # 查找data数组
            if 'data' in data and isinstance(data['data'], (list, tuple)):
                print(f"🔍 找到data数组，长度: {len(data['data'])}")

                for i, item in enumerate(data['data']):
                    if isinstance(item, dict):
                        print(f"🔍 处理data[{i}]: {list(item.keys())}")

                        # 查找waterData中的站点信息
                        if 'waterData' in item:
                            water_data = item['waterData']
                            if isinstance(water_data, dict):
                                # 查找各种站点类型
                                for key in ['cx', 'cjj', 'qt', 'cbz']:
                                    if key in water_data and isinstance(water_data[key], (list, tuple)):
                                        print(f"🔍 找到{key}数组，长度: {len(water_data[key])}")
                                        for 站点 in water_data[key]:
                                            if isinstance(站点, dict):
                                                站点信息 = self.解析站点信息(站点)
                                                if 站点信息:
                                                    水情列表.append(站点信息)

                        # 查找ThData
                        if 'ThData' in item:
                            th_data = item['ThData']
                            if isinstance(th_data, str):
                                import re
                                data_pattern = r'([0-9.]+)\(([^)]+)\)'
                                data_match = re.search(data_pattern, th_data)
                                if data_match:
                                    水位值, 时间 = data_match.groups()
                                    水情列表.append({
                                        "站点名称": "ThData数据",
                                        "当前水位": f"{水位值}m",
                                        "更新时间": 时间,
                                        "数据来源": "ThData字段"
                                    })

            print(f"🔍 总共提取到 {len(水情列表)} 个站点")
            return 水情列表 if 水情列表 else [{"说明": "未找到水情数据"}]

        except Exception as e:
            print(f"🔍 提取数据失败: {str(e)}")
            return [{"说明": f"提取数据失败: {str(e)}"}]

    def 解析站点信息(self, 站点数据):
        """解析单个站点信息"""
        try:
            if not isinstance(站点数据, dict):
                return None

            站点信息 = {}

            # 基本字段映射
            字段映射 = {
                'zh': '站点ID',
                'name': '站点名称',
                'zm': '站点名称',
                'sw': '当前水位',
                'xx': '限制水位',
                'kr': '库容',
                'lon': '经度',
                'lat': '纬度',
                'city': '城市',
                'county': '区县',
                'time': '更新时间',
                'index': '序号'
            }

            # 提取基本信息
            for js_key, cn_key in 字段映射.items():
                if js_key in 站点数据:
                    value = 站点数据[js_key]
                    if value is not None and value != "":
                        站点信息[cn_key] = value

            # 处理info字段中的详细信息
            if 'info' in 站点数据 and isinstance(站点数据['info'], dict):
                info = 站点数据['info']
                for js_key, cn_key in 字段映射.items():
                    if js_key in info and cn_key not in 站点信息:
                        value = info[js_key]
                        if value is not None and value != "":
                            站点信息[cn_key] = value

            # 确保有基本信息才返回
            if '站点ID' in 站点信息 or '站点名称' in 站点信息:
                return 站点信息
            else:
                return None

        except Exception as e:
            print(f"🔍 解析站点信息失败: {str(e)}")
            return None

    def 备用解析方法(self, html内容):
        """备用的正则表达式解析方法"""
        import re

        try:
            # 查找NUXT函数调用的参数
            pattern = r'window\.__NUXT__=\(function\([^)]*\)\{.*?\}\(([^)]+)\)\);'
            match = re.search(pattern, html内容, re.DOTALL)

            if match:
                print("🔍 备用方法: 找到NUXT函数参数")
                params_str = match.group(1)
                水情数据 = self.解析函数参数(params_str)
                if 水情数据:
                    return 水情数据

            # 查找简单的ThData
            th_data_pattern = r'ThData:"([^"]*)"'
            th_data_match = re.search(th_data_pattern, html内容)

            if th_data_match:
                print("🔍 备用方法: 找到ThData数据")
                th_data = th_data_match.group(1)
                data_pattern = r'([0-9.]+)\(([^)]+)\)'
                data_match = re.search(data_pattern, th_data)

                if data_match:
                    水位值, 时间 = data_match.groups()
                    return [{
                        "当前水位": f"{水位值}m",
                        "更新时间": 时间,
                        "说明": "从ThData提取的备用数据"
                    }]

            return [{"说明": "未找到可解析的水情数据"}]

        except Exception as e:
            return [{"说明": f"备用解析方法异常: {str(e)}"}]

    def 直接提取函数参数(self, html内容):
        """直接使用正则表达式提取函数参数"""
        import re

        # 查找NUXT函数调用的参数
        # 匹配: window.__NUXT__=(function(...){...})(参数列表);
        pattern = r'window\.__NUXT__=\(function\([^)]*\)\{.*?\}\(([^)]+)\)\);'
        match = re.search(pattern, html内容, re.DOTALL)

        if match:
            print("🔍 直接提取: 找到函数参数")
            params_str = match.group(1)
            return self.解析函数参数(params_str)

        return None



    def 递归查找水情数据(self, data, path=""):
        """递归查找JSON数据中的水情信息"""
        结果 = []

        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key

                # 查找可能包含水情数据的字段
                if any(keyword in str(key).lower() for keyword in ['water', 'station', 'reservoir', 'dam', 'level', 'thdata']):
                    print(f"🔍 找到可能的水情字段: {current_path}")

                    # 特殊处理ThData字段
                    if key == 'ThData' and isinstance(value, str):
                        import re
                        data_pattern = r'([0-9.]+)\(([^)]+)\)'
                        data_match = re.search(data_pattern, value)
                        if data_match:
                            水位值, 时间 = data_match.groups()
                            结果.append({
                                "当前水位": f"{水位值}m",
                                "更新时间": 时间,
                                "数据来源": "ThData字段",
                                "原始数据": value
                            })

                    # 特殊处理waterData字段
                    elif key == 'waterData' and isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, list) and sub_value:  # 非空数组
                                结果.append({
                                    "数据类型": sub_key,
                                    "数据内容": sub_value,
                                    "数据来源": f"waterData.{sub_key}",
                                    "数据长度": len(sub_value)
                                })

                # 递归查找
                子结果 = self.递归查找水情数据(value, current_path)
                结果.extend(子结果)

        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                子结果 = self.递归查找水情数据(item, current_path)
                结果.extend(子结果)

        elif isinstance(data, str):
            # 检查是否包含水库、站点等关键词
            if any(keyword in data for keyword in ['水库', '站', '闸', '堰', '河道']):
                结果.append({
                    "路径": path,
                    "内容": data,
                    "说明": "从JSON数据中提取的可能站点信息"
                })

            # 检查是否是水位数据格式
            import re
            if re.match(r'[0-9.]+\([^)]+\)', data):
                data_pattern = r'([0-9.]+)\(([^)]+)\)'
                data_match = re.search(data_pattern, data)
                if data_match:
                    水位值, 时间 = data_match.groups()
                    结果.append({
                        "路径": path,
                        "当前水位": f"{水位值}m",
                        "更新时间": 时间,
                        "说明": "从字符串中提取的水位数据"
                    })

        return 结果

    def 从字符串提取数据(self, text):
        """从字符串中直接提取水情数据"""
        import re

        结果 = []

        # 查找ThData字段，这似乎包含水位数据
        th_data_pattern = r'ThData:"([^"]*)"'
        th_data_match = re.search(th_data_pattern, text)

        if th_data_match:
            th_data = th_data_match.group(1)
            print(f"🔍 找到ThData: {th_data}")

            # 解析ThData中的数据，格式可能是: "3.87(2025-08-05 08:00:00)"
            data_pattern = r'([0-9.]+)\(([^)]+)\)'
            data_match = re.search(data_pattern, th_data)

            if data_match:
                水位值, 时间 = data_match.groups()
                结果.append({
                    "当前水位": f"{水位值}m",
                    "更新时间": 时间,
                    "说明": "从ThData字段提取"
                })

        # 查找waterData中的数组数据
        water_data_pattern = r'waterData:\{([^}]+)\}'
        water_data_match = re.search(water_data_pattern, text)

        if water_data_match:
            water_data = water_data_match.group(1)
            print(f"🔍 找到waterData: {water_data}")

            # 查找各种数组：cjj, qt, cx, cbz
            arrays = ['cjj', 'qt', 'cx', 'cbz']
            for array_name in arrays:
                array_pattern = f'{array_name}:\\[([^\\]]*)\\]'
                array_match = re.search(array_pattern, water_data)
                if array_match:
                    array_content = array_match.group(1)
                    if array_content.strip():  # 如果数组不为空
                        结果.append({
                            "数据类型": array_name,
                            "内容": array_content,
                            "说明": f"从waterData.{array_name}提取"
                        })

        return 结果

    def 解析函数参数(self, params_str):
        """解析NUXT函数调用的参数列表，提取水库信息 - 基于18_Full.txt优化"""
        import re

        print(f"🔍 参数字符串预览: {params_str[:200]}...")

        # 基于18_Full.txt的实际数据，使用更精确的参数分割
        # 实际格式：(null,"-","1","吴兴区","浙江省","湖州市","08-05T09:55:00","2025-08-05T09:55:00","德清县","0.0",0,"PZ","1.86",1.86,"ZZ","长兴县","南浔区","2.46",2.46,"苕溪水系","湖-吴兴","运河水系","330502","1.99","-0.0","1.98","RR"," 0 Q"," 0  ","08-05T09:50:00","2025-08-05T09:50:00","湖-德清","330521","安吉县",1,"63201751","幻溇闸（闸下游）","1.97",120.245,30.926389,"PZQ","ZQ",2,"63201801","大钱闸（闸下游）",120.191139,30.930111,3,"63201850","小梅口","2.00",120.102593,30.957222,3.8,4,...)

        # 使用更精确的正则表达式分割参数
        param_pattern = r'(null|"[^"]*"|-?[0-9]+\.?[0-9]*)'
        params = re.findall(param_pattern, params_str)

        print(f"🔍 解析出 {len(params)} 个参数")
        print(f"🔍 前20个参数: {params[:20]}")

        if len(params) < 30:
            print("🔍 参数数量不足，可能解析失败")
            return []

        # 清理参数，去掉引号并转换数据类型
        cleaned_params = []
        for param in params:
            if param == 'null':
                cleaned_params.append(None)
            elif param.startswith('"') and param.endswith('"'):
                cleaned_params.append(param[1:-1])  # 去掉引号
            else:
                try:
                    if '.' in param:
                        cleaned_params.append(float(param))
                    else:
                        cleaned_params.append(int(param))
                except:
                    cleaned_params.append(param)

        # 从实际输出分析参数结构：
        # ['null', '"黄岩区"', '"-"', '"1"', '"台-黄岩"', '"椒江水系"', '"PZ"', '"331003"', '"RR"', '"浙江省"', '"台州市"', '"70405712"', '"08-05T09:55:00"', '"飞水岩水库"', '"-0.0"', '"153.63"', 121.097038, 28.667364, '" 0"', '"2025-08-05T09:55:00"']

        基础信息 = {
            "区县": cleaned_params[1] if len(cleaned_params) > 1 else "",
            "省份": cleaned_params[9] if len(cleaned_params) > 9 else "",
            "城市": cleaned_params[10] if len(cleaned_params) > 10 else "",
            "更新时间": cleaned_params[19] if len(cleaned_params) > 19 else "",
        }

        print(f"🔍 修正后基础信息: {基础信息}")

        # 基于数据结构模式解析，不依赖关键词
        水库列表 = []
        水库序号 = 1

        print(f"🔍 开始基于结构模式解析参数...")
        print(f"🔍 参数样本: {cleaned_params[200:220]}")  # 显示中间部分的参数样本

        i = 0
        while i + 6 < len(cleaned_params):
            try:
                # 识别站点ID模式（6位以上的数字字符串或包含字母的编码）
                current_param = cleaned_params[i]

                is_station_id = False
                if isinstance(current_param, str):
                    # 模式1: 纯数字ID（6-8位）
                    if current_param.isdigit() and 6 <= len(current_param) <= 8:
                        is_station_id = True
                    # 模式2: 包含字母的ID（如7041JB44）
                    elif (len(current_param) >= 6 and
                          any(c.isdigit() for c in current_param) and
                          any(c.isalpha() for c in current_param)):
                        is_station_id = True

                if is_station_id:
                    # 按固定结构解析：站点ID → 站点名称 → 其他数据 → 经度 → 纬度
                    站点ID = current_param

                    # 查找站点名称（通常在ID后的1-3个位置内）
                    站点名称 = None
                    for offset in range(1, 4):
                        if i + offset < len(cleaned_params):
                            candidate = cleaned_params[i + offset]
                            if (isinstance(candidate, str) and
                                len(candidate) >= 2 and
                                not candidate.isdigit() and
                                candidate not in ['PZ', 'RR', 'ZZ', 'ZQ', 'DD', 'TT', '-', '1', '2', '0']):
                                站点名称 = candidate
                                break

                    if 站点名称:
                        # 在后续参数中查找经纬度（两个连续的浮点数）
                        经度 = None
                        纬度 = None
                        水位值 = "N/A"

                        for j in range(i + 1, min(i + 15, len(cleaned_params))):
                            param = cleaned_params[j]

                            # 查找经纬度（连续的两个浮点数，在合理范围内）
                            if isinstance(param, (int, float)):
                                if 119 <= param <= 122 and 经度 is None:
                                    # 检查下一个参数是否是纬度
                                    if (j + 1 < len(cleaned_params) and
                                        isinstance(cleaned_params[j + 1], (int, float)) and
                                        27 <= cleaned_params[j + 1] <= 31):
                                        经度 = param
                                        纬度 = cleaned_params[j + 1]
                                        break

                            # 查找水位值（数字字符串或小数）
                            elif isinstance(param, str) and 水位值 == "N/A":
                                try:
                                    val = float(param)
                                    if 0 <= val <= 2000:  # 合理的水位范围
                                        水位值 = param
                                except:
                                    pass

                        # 如果找到了完整信息就记录
                        if 经度 is not None and 纬度 is not None:
                            水库信息 = {
                                "序号": 水库序号,
                                "站点ID": 站点ID,
                                "站点名称": 站点名称,
                                "当前水位": f"{水位值}m",
                                "经度": 经度,
                                "纬度": 纬度,
                                "区县": 基础信息["区县"],
                                "城市": 基础信息["城市"],
                                "省份": 基础信息["省份"],
                                "更新时间": 基础信息["更新时间"],
                            }

                            水库列表.append(水库信息)
                            水库序号 += 1
                            print(f"🔍 解析站点 #{水库序号-1}: {站点名称} (ID: {站点ID}) 位置: {i}")

                            # 跳过已处理的参数
                            i += 10  # 跳过更多参数避免重复
                            continue

                i += 1

            except (ValueError, IndexError) as e:
                print(f"🔍 解析位置 {i} 时出错: {e}")
                i += 1
                continue

        print(f"🔍 成功解析出 {len(水库列表)} 个站点")

        return 水库列表


def main():
    """主函数"""
    print("🌊 浙江省水情数据获取工具")
    print("=" * 40)

    # 字段映射配置
    站点类型映射 = {
        'RR': '水库',
        'ZZ': '河道',
        'ZQ': '河道',
        'DD': '堰闸',
        'TT': '潮汐'
    }

    水库类型映射 = {
        '4': '大型水库',
        '5': '大型水库',
        '3': '中型水库',
        '2': '小一',
        '1': '其他(含小二)',
        '9': '其他(含小二)'
    }

    # 查询配置参数
    城市 = "浙江省"
    区县 = ""
    站点类型 = 'RR,ZZ,ZQ,DD,TT,'  # 所有站点类型
    水库类型 = '4,5,3,2,1,9,'     # 所有水库类型

    工具 = 水情工具()

    print(f"\n查询: {城市} {区县 or '全市'}")
    print(f"站点类型: {站点类型}")
    print(f"水库类型: {水库类型}")

    结果 = 工具.获取数据(城市, 区县, 站点类型, 水库类型)

    if "错误" in 结果:
        print(f"❌ {结果['错误']}")
    else:
        print(f"✅ 成功 - 数据长度: {结果['数据长度']:,} 字符")

        # 显示解析的水情数据
        水情数据 = 结果.get('水情数据', [])
        if 水情数据:
            print(f"\n🌊 解析出的水情数据:")
            for i, 数据 in enumerate(水情数据, 1):
                if '站点名称' in 数据:
                    print(f"  {i}. {数据['站点名称']} (ID: {数据.get('站点ID', 'N/A')})")
                    print(f"     当前水位: {数据.get('当前水位', 'N/A')}")
                    print(f"     限制水位: {数据.get('限制水位', 'N/A')}")
                    print(f"     警戒水位: {数据.get('警戒水位', 'N/A')}")
                    print(f"     保证水位: {数据.get('保证水位', 'N/A')}")
                    print(f"     超限水位: {数据.get('超限水位', 'N/A')}")
                    print(f"     正常高程: {数据.get('正常高程', 'N/A')}")
                    print(f"     汛限高程: {数据.get('汛限高程', 'N/A')}")
                    print(f"     库容: {数据.get('库容', 'N/A')}")
                    print(f"     位置: {数据.get('经度', 0)}, {数据.get('纬度', 0)}")
                    print()
                else:
                    print(f"  {i}. {数据}")
        else:
            print(f"\n⚠️ 未解析出具体数据")



if __name__ == "__main__":
    main()
