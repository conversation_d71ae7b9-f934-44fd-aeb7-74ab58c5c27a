#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from datetime import datetime


class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        
    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """
        获取水情数据

        """

        # 初始化会话
        try:
            self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=10)
            self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=10)
        except:
            return {"错误": "网络连接失败"}

        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': 城市,
            'ssx': 区县,
            'zl': 站点类型,
            'sklx': 水库类型,
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',  # 固定值
            'ly': '',
            'zm': '',  # 站名固定为空
            'cjly': '',
            'bx': 0
        }
        
        
        # 发送请求
        try:
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)

            # 解析数据
            水情数据 = self.解析数据(响应.text)

            return {
                "状态": "成功",
                "数据长度": len(响应.text),
                "水情数据": 水情数据,
                "查询参数": 参数,
                "URL": 响应.url
            }
        except Exception as e:
            return {"错误": str(e)}

    def 解析数据(self, html内容):
        """从HTML中解析水情数据"""
        import re
        import json

        try:
            # 查找window.__NUXT__脚本
            if 'window.__NUXT__=' not in html内容:
                return [{"说明": "未找到NUXT数据"}]

            # 提取NUXT脚本内容
            start_idx = html内容.find('window.__NUXT__=')
            end_idx = html内容.find(';</script>', start_idx)

            if start_idx == -1 or end_idx == -1:
                return [{"说明": "NUXT脚本格式异常"}]

            nuxt_script = html内容[start_idx:end_idx]

            # 调试：输出脚本内容的前500个字符
            print(f"🔍 NUXT脚本内容预览: {nuxt_script[:500]}...")

            # 尝试多种解析方式

            # 方式1: 解析函数调用格式的NUXT数据
            # 格式: window.__NUXT__=(function(a){return {...}}(null))

            # 查找函数返回的对象
            function_pattern = r'window\.__NUXT__=\(function\([^)]*\)\{return\s+(\{.+?\})\}\([^)]*\)\)'
            function_match = re.search(function_pattern, nuxt_script, re.DOTALL)

            if function_match:
                try:
                    # 提取函数返回的JSON对象
                    json_str = function_match.group(1)
                    # 替换参数占位符
                    json_str = json_str.replace(',a,', ',null,').replace(':a,', ':null,').replace(':a}', ':null}')

                    # 修复JavaScript对象格式为JSON格式
                    # 给属性名加上双引号
                    json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

                    print(f"🔍 修复后的JSON字符串预览: {json_str[:200]}...")

                    nuxt_data = json.loads(json_str)
                    print(f"🔍 成功解析JSON数据，类型: {type(nuxt_data)}")

                    # 递归查找水情数据
                    水情数据 = self.递归查找水情数据(nuxt_data)
                    if 水情数据:
                        return 水情数据

                except json.JSONDecodeError as e:
                    print(f"🔍 JSON解析失败: {e}")
                    # 如果还是失败，尝试直接从字符串中提取数据
                    水情数据 = self.从字符串提取数据(json_str)
                    if 水情数据:
                        return 水情数据

            # 方式1.5: 尝试简单的JSON解析
            json_pattern = r'window\.__NUXT__=(.+)'
            json_match = re.search(json_pattern, nuxt_script)

            if json_match:
                try:
                    # 尝试解析为JSON
                    json_str = json_match.group(1)
                    # 移除可能的函数调用包装
                    if json_str.startswith('(') and json_str.endswith(')'):
                        json_str = json_str[1:-1]

                    nuxt_data = json.loads(json_str)
                    print(f"🔍 成功解析JSON数据，类型: {type(nuxt_data)}")

                    # 递归查找水情数据
                    水情数据 = self.递归查找水情数据(nuxt_data)
                    if 水情数据:
                        return 水情数据

                except json.JSONDecodeError as e:
                    print(f"🔍 JSON解析失败: {e}")

            # 方式2: 解析函数参数列表（新的完整解析方式）
            # 格式：(null,"黄岩区","-","1","台-黄岩","08-05T00:15:00","椒江水系","PZ","331003","RR","浙江省","台州市","2025-08-05T00:15:00","-0.0"," 0","70405712","飞水岩水库","153.67",121.097038,28.667364,...)

            # 提取函数调用的参数部分
            # 更灵活的正则表达式来匹配函数调用
            function_call_pattern = r'window\.__NUXT__=\(function\([^)]*\)\{.*?\}\(([^)]+)\)\)'
            function_call_match = re.search(function_call_pattern, nuxt_script, re.DOTALL)

            if function_call_match:
                print("🔍 找到函数调用参数")
                params_str = function_call_match.group(1)

                # 解析参数列表
                水情数据 = self.解析函数参数(params_str)
                if 水情数据:
                    return 水情数据
            else:
                print("🔍 未找到函数调用参数，尝试直接提取参数")
                # 尝试直接从脚本末尾提取参数
                # 查找最后的括号内容
                last_paren_pattern = r'\(([^)]+)\)\s*;\s*$'
                last_paren_match = re.search(last_paren_pattern, nuxt_script)

                if last_paren_match:
                    print("🔍 找到末尾参数")
                    params_str = last_paren_match.group(1)

                    # 解析参数列表
                    水情数据 = self.解析函数参数(params_str)
                    if 水情数据:
                        return 水情数据

            # 方式3: 查找单个站点的参数格式（原有方式，作为备用）
            param_pattern = r'\(null,"([^"]*)","-","([^"]*)","([^"]*?)","([^"]*)",([0-9.]+),([0-9.]+),"([^"]*)"\)'
            param_match = re.search(param_pattern, nuxt_script)

            if param_match:
                print("🔍 找到单站点参数格式数据")
                # 提取匹配的参数
                区县, 站点ID, 站点名称, 水位, 经度, 纬度, 类型 = param_match.groups()

                # 从NUXT脚本中提取更多水位信息
                xx_match = re.search(r'xx:"([^"]*)"', nuxt_script)      # 限制水位
                kr_match = re.search(r'kr:"([^"]*)"', nuxt_script)      # 库容
                jjsw_match = re.search(r'jjsw:([0-9.]+|null)', nuxt_script)   # 警戒水位
                bzsw_match = re.search(r'bzsw:([0-9.]+|null)', nuxt_script)   # 保证水位
                zcsw_match = re.search(r'zcsw:([0-9.]+|null)', nuxt_script)   # 超限水位
                ztgc_match = re.search(r'ztgc:([0-9.]+|null)', nuxt_script)   # 正常高程
                ytgc_match = re.search(r'ytgc:([0-9.]+|null)', nuxt_script)   # 汛限高程

                限制水位 = xx_match.group(1) if xx_match else "N/A"
                库容 = kr_match.group(1) if kr_match else "N/A"
                警戒水位 = jjsw_match.group(1) if jjsw_match and jjsw_match.group(1) != 'null' else "N/A"
                保证水位 = bzsw_match.group(1) if bzsw_match and bzsw_match.group(1) != 'null' else "N/A"
                超限水位 = zcsw_match.group(1) if zcsw_match and zcsw_match.group(1) != 'null' else "N/A"
                正常高程 = ztgc_match.group(1) if ztgc_match and ztgc_match.group(1) != 'null' else "N/A"
                汛限高程 = ytgc_match.group(1) if ytgc_match and ytgc_match.group(1) != 'null' else "N/A"

                return [{
                    "区县": 区县,
                    "站点ID": 站点ID,
                    "站点名称": 站点名称,
                    "当前水位": f"{水位}m",
                    "限制水位": f"{限制水位}m" if 限制水位 != "N/A" else "N/A",
                    "警戒水位": f"{警戒水位}m" if 警戒水位 != "N/A" else "N/A",
                    "保证水位": f"{保证水位}m" if 保证水位 != "N/A" else "N/A",
                    "超限水位": f"{超限水位}m" if 超限水位 != "N/A" else "N/A",
                    "正常高程": f"{正常高程}m" if 正常高程 != "N/A" else "N/A",
                    "汛限高程": f"{汛限高程}m" if 汛限高程 != "N/A" else "N/A",
                    "库容": f"{库容}万m³" if 库容 != "N/A" else "N/A",
                    "经度": float(经度),
                    "纬度": float(纬度),
                    "类型": 类型
                }]
            else:
                # 方式3: 查找其他可能的数据格式
                print("🔍 尝试查找其他数据格式...")

                # 查找所有可能的站点数据
                站点数据列表 = []

                # 查找站点名称模式
                站点名称模式 = r'"([^"]*水库|[^"]*站|[^"]*闸|[^"]*堰)"'
                站点名称匹配 = re.findall(站点名称模式, nuxt_script)

                if 站点名称匹配:
                    print(f"🔍 找到可能的站点名称: {站点名称匹配}")
                    for 站点名称 in 站点名称匹配:
                        站点数据列表.append({"站点名称": 站点名称, "说明": "从站点名称模式提取"})

                if 站点数据列表:
                    return 站点数据列表

                return [{"说明": f"未找到NUXT参数，脚本长度: {len(nuxt_script)}", "脚本预览": nuxt_script[:200]}]

        except Exception as e:
            return [{"说明": f"数据解析异常: {str(e)}"}]

    def 递归查找水情数据(self, data, path=""):
        """递归查找JSON数据中的水情信息"""
        结果 = []

        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key

                # 查找可能包含水情数据的字段
                if any(keyword in str(key).lower() for keyword in ['water', 'station', 'reservoir', 'dam', 'level', 'thdata']):
                    print(f"🔍 找到可能的水情字段: {current_path}")

                    # 特殊处理ThData字段
                    if key == 'ThData' and isinstance(value, str):
                        import re
                        data_pattern = r'([0-9.]+)\(([^)]+)\)'
                        data_match = re.search(data_pattern, value)
                        if data_match:
                            水位值, 时间 = data_match.groups()
                            结果.append({
                                "当前水位": f"{水位值}m",
                                "更新时间": 时间,
                                "数据来源": "ThData字段",
                                "原始数据": value
                            })

                    # 特殊处理waterData字段
                    elif key == 'waterData' and isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if isinstance(sub_value, list) and sub_value:  # 非空数组
                                结果.append({
                                    "数据类型": sub_key,
                                    "数据内容": sub_value,
                                    "数据来源": f"waterData.{sub_key}",
                                    "数据长度": len(sub_value)
                                })

                # 递归查找
                子结果 = self.递归查找水情数据(value, current_path)
                结果.extend(子结果)

        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]" if path else f"[{i}]"
                子结果 = self.递归查找水情数据(item, current_path)
                结果.extend(子结果)

        elif isinstance(data, str):
            # 检查是否包含水库、站点等关键词
            if any(keyword in data for keyword in ['水库', '站', '闸', '堰', '河道']):
                结果.append({
                    "路径": path,
                    "内容": data,
                    "说明": "从JSON数据中提取的可能站点信息"
                })

            # 检查是否是水位数据格式
            import re
            if re.match(r'[0-9.]+\([^)]+\)', data):
                data_pattern = r'([0-9.]+)\(([^)]+)\)'
                data_match = re.search(data_pattern, data)
                if data_match:
                    水位值, 时间 = data_match.groups()
                    结果.append({
                        "路径": path,
                        "当前水位": f"{水位值}m",
                        "更新时间": 时间,
                        "说明": "从字符串中提取的水位数据"
                    })

        return 结果

    def 从字符串提取数据(self, text):
        """从字符串中直接提取水情数据"""
        import re

        结果 = []

        # 查找ThData字段，这似乎包含水位数据
        th_data_pattern = r'ThData:"([^"]*)"'
        th_data_match = re.search(th_data_pattern, text)

        if th_data_match:
            th_data = th_data_match.group(1)
            print(f"🔍 找到ThData: {th_data}")

            # 解析ThData中的数据，格式可能是: "3.87(2025-08-05 08:00:00)"
            data_pattern = r'([0-9.]+)\(([^)]+)\)'
            data_match = re.search(data_pattern, th_data)

            if data_match:
                水位值, 时间 = data_match.groups()
                结果.append({
                    "当前水位": f"{水位值}m",
                    "更新时间": 时间,
                    "说明": "从ThData字段提取"
                })

        # 查找waterData中的数组数据
        water_data_pattern = r'waterData:\{([^}]+)\}'
        water_data_match = re.search(water_data_pattern, text)

        if water_data_match:
            water_data = water_data_match.group(1)
            print(f"🔍 找到waterData: {water_data}")

            # 查找各种数组：cjj, qt, cx, cbz
            arrays = ['cjj', 'qt', 'cx', 'cbz']
            for array_name in arrays:
                array_pattern = f'{array_name}:\\[([^\\]]*)\\]'
                array_match = re.search(array_pattern, water_data)
                if array_match:
                    array_content = array_match.group(1)
                    if array_content.strip():  # 如果数组不为空
                        结果.append({
                            "数据类型": array_name,
                            "内容": array_content,
                            "说明": f"从waterData.{array_name}提取"
                        })

        return 结果

    def 解析函数参数(self, params_str):
        """解析NUXT函数调用的参数列表，提取水库信息"""
        import re

        print(f"🔍 参数字符串预览: {params_str[:200]}...")

        # 使用正则表达式分割参数，处理字符串中的逗号
        # 这个正则会匹配：数字、字符串（包含引号）、null等
        param_pattern = r'(null|"[^"]*"|-?[0-9]+\.?[0-9]*)'
        params = re.findall(param_pattern, params_str)

        print(f"🔍 解析出 {len(params)} 个参数")

        if len(params) < 20:
            print("🔍 参数数量不足，可能解析失败")
            return []

        # 根据您提供的数据分析参数结构：
        # (null,"黄岩区","-","1","台-黄岩","08-05T00:15:00","椒江水系","PZ","331003","RR","浙江省","台州市","2025-08-05T00:15:00","-0.0"," 0",
        #  "70405712","飞水岩水库","153.67",121.097038,28.667364,
        #  "70405742","水竹水库","210.69",121.195051,28.71574,"2",-.01,
        #  "70405744","黄坦水库","18.31",121.196961,28.699041,
        #  "7041JB44","白沙园水库","93.38",121.031007,28.495483,
        #  "7041JB46","柔极溪二级水库","354.02",120.940955,28.683597)

        # 前15个是基础信息
        基础信息 = {
            "区县": params[1].strip('"') if len(params) > 1 else "",
            "城市": params[11].strip('"') if len(params) > 11 else "",
            "省份": params[10].strip('"') if len(params) > 10 else "",
            "更新时间": params[12].strip('"') if len(params) > 12 else "",
        }

        print(f"🔍 基础信息: {基础信息}")

        # 从第14个参数开始是水库信息
        # 根据实际数据分析：(null,"黄岩区","-","1","台-黄岩","08-05T08:45:00","椒江水系","PZ","331003","RR","浙江省","台州市","2025-08-05T08:45:00"," 0",
        #                   "70405712","飞水岩水库","0.0","153.63",121.097038,28.667364,0,
        #                   "70405742","水竹水库","-0.0","210.61",121.195051,28.71574,...)

        水库列表 = []
        水库起始索引 = 14  # 从第14个参数开始

        i = 水库起始索引
        水库序号 = 1

        while i + 5 < len(params):
            try:
                站点ID = params[i].strip('"')
                站点名称 = params[i + 1].strip('"')
                水位1 = params[i + 2].strip('"')  # 可能是相对水位
                水位2 = params[i + 3].strip('"')  # 可能是绝对水位
                经度 = float(params[i + 4].strip('"'))
                纬度 = float(params[i + 5].strip('"'))

                # 选择更合理的水位值
                if 水位2 and 水位2 != "0.0" and 水位2 != "-0.0":
                    当前水位 = 水位2
                else:
                    当前水位 = 水位1

                水库信息 = {
                    "序号": 水库序号,
                    "站点ID": 站点ID,
                    "站点名称": 站点名称,
                    "当前水位": f"{当前水位}m",
                    "相对水位": f"{水位1}m",
                    "绝对水位": f"{水位2}m",
                    "经度": 经度,
                    "纬度": 纬度,
                    "区县": 基础信息["区县"],
                    "城市": 基础信息["城市"],
                    "省份": 基础信息["省份"],
                    "更新时间": 基础信息["更新时间"],
                }

                水库列表.append(水库信息)
                水库序号 += 1

                # 检查下一组参数的间隔
                # 有些水库可能有额外的参数
                if i + 6 < len(params) and params[i + 6].isdigit():
                    # 如果有额外的数字参数，跳过它
                    i += 7
                else:
                    i += 6

            except (ValueError, IndexError) as e:
                print(f"🔍 解析第{水库序号}个水库时出错: {e}")
                print(f"🔍 当前索引: {i}, 参数: {params[i:i+6] if i+6 <= len(params) else params[i:]}")
                break

        print(f"🔍 成功解析出 {len(水库列表)} 个水库")

        return 水库列表


def main():
    """主函数"""
    print("🌊 浙江省水情数据获取工具")
    print("=" * 40)

    # 字段映射配置
    站点类型映射 = {
        'RR': '水库',
        'ZZ': '河道',
        'ZQ': '河道',
        'DD': '堰闸',
        'TT': '潮汐'
    }

    水库类型映射 = {
        '4': '大型水库',
        '5': '大型水库',
        '3': '中型水库',
        '2': '小一',
        '1': '其他(含小二)',
        '9': '其他(含小二)'
    }

    # 查询配置参数
    城市 = "台州市"
    区县 = "黄岩区"
    站点类型 = 'RR,ZZ,ZQ,DD,TT,'  # 所有站点类型
    水库类型 = '4,5,3,2,1,9,'     # 所有水库类型

    工具 = 水情工具()

    print(f"\n查询: {城市} {区县 or '全市'}")
    print(f"站点类型: {站点类型}")
    print(f"水库类型: {水库类型}")

    结果 = 工具.获取数据(城市, 区县, 站点类型, 水库类型)

    if "错误" in 结果:
        print(f"❌ {结果['错误']}")
    else:
        print(f"✅ 成功 - 数据长度: {结果['数据长度']:,} 字符")

        # 显示解析的水情数据
        水情数据 = 结果.get('水情数据', [])
        if 水情数据:
            print(f"\n🌊 解析出的水情数据:")
            for i, 数据 in enumerate(水情数据, 1):
                if '站点名称' in 数据:
                    print(f"  {i}. {数据['站点名称']} (ID: {数据.get('站点ID', 'N/A')})")
                    print(f"     当前水位: {数据.get('当前水位', 'N/A')}")
                    print(f"     限制水位: {数据.get('限制水位', 'N/A')}")
                    print(f"     警戒水位: {数据.get('警戒水位', 'N/A')}")
                    print(f"     保证水位: {数据.get('保证水位', 'N/A')}")
                    print(f"     超限水位: {数据.get('超限水位', 'N/A')}")
                    print(f"     正常高程: {数据.get('正常高程', 'N/A')}")
                    print(f"     汛限高程: {数据.get('汛限高程', 'N/A')}")
                    print(f"     库容: {数据.get('库容', 'N/A')}")
                    print(f"     位置: {数据.get('经度', 0)}, {数据.get('纬度', 0)}")
                    print()
                else:
                    print(f"  {i}. {数据}")
        else:
            print(f"\n⚠️ 未解析出具体数据")



if __name__ == "__main__":
    main()
